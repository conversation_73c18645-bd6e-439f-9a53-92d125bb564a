/**
 * 插件生命周期管理器
 */

import type { MicroCore } from '@micro-core/core'
import type { MicroCoreError } from '@micro-core/shared'
import { PluginBase, PluginState, type PluginConfig, type PluginContext, type PluginEvent } from './plugin-base'

/**
 * 生命周期钩子类型
 */
export type LifecycleHook = 'beforeInstall' | 'afterInstall' | 'beforeUninstall' | 'afterUninstall' |
  'beforeActivate' | 'afterActivate' | 'beforeDeactivate' | 'afterDeactivate'

/**
 * 生命周期钩子函数
 */
export type LifecycleHookFunction = (plugin: PluginBase, context: PluginContext) => Promise<void>

/**
 * 生命周期管理器
 */
export class PluginLifecycleManager {
  private hooks: Map<LifecycleHook, LifecycleHookFunction[]> = new Map()
  private core: MicroCore

  constructor(core: MicroCore) {
    this.core = core
    this.initializeHooks()
  }

  /**
   * 初始化生命周期钩子
   */
  private initializeHooks(): void {
    const hookTypes: LifecycleHook[] = [
      'beforeInstall', 'afterInstall', 'beforeUninstall', 'afterUninstall',
      'beforeActivate', 'afterActivate', 'beforeDeactivate', 'afterDeactivate'
    ]

    hookTypes.forEach(hook => {
      this.hooks.set(hook, [])
    })
  }

  /**
   * 注册生命周期钩子
   * @param hook 钩子类型
   * @param fn 钩子函数
   */
  registerHook(hook: LifecycleHook, fn: LifecycleHookFunction): void {
    const hooks = this.hooks.get(hook)
    if (hooks) {
      hooks.push(fn)
    }
  }

  /**
   * 取消注册生命周期钩子
   * @param hook 钩子类型
   * @param fn 钩子函数
   */
  unregisterHook(hook: LifecycleHook, fn: LifecycleHookFunction): void {
    const hooks = this.hooks.get(hook)
    if (hooks) {
      const index = hooks.indexOf(fn)
      if (index > -1) {
        hooks.splice(index, 1)
      }
    }
  }

  /**
   * 执行生命周期钩子
   * @param hook 钩子类型
   * @param plugin 插件实例
   * @param context 插件上下文
   */
  async executeHook(hook: LifecycleHook, plugin: PluginBase, context: PluginContext): Promise<void> {
    const hooks = this.hooks.get(hook)
    if (!hooks || hooks.length === 0) {
      return
    }

    for (const hookFn of hooks) {
      try {
        await hookFn(plugin, context)
      } catch (error) {
        console.error(`执行插件生命周期钩子 ${hook} 时出错:`, error)
        throw error
      }
    }
  }

  /**
   * 安装插件
   * @param plugin 插件实例
   * @param config 插件配置
   */
  async installPlugin(plugin: PluginBase, config: PluginConfig = {}): Promise<void> {
    const context: PluginContext = {
      core: this.core,
      config,
      state: PluginState.INSTALLING,
      metadata: plugin.getMetadata(),
      createdAt: Date.now()
    }

    plugin.setContext(context)

    try {
      // 执行安装前钩子
      await this.executeHook('beforeInstall', plugin, context)

      // 执行插件安装
      await plugin.install(this.core, config)

      // 更新状态
      context.state = PluginState.INSTALLED
      context.installedAt = Date.now()

      // 执行安装后钩子
      await this.executeHook('afterInstall', plugin, context)

      // 触发安装事件
      plugin['emit']({
        type: 'install',
        plugin,
        timestamp: Date.now()
      })

    } catch (error) {
      context.state = PluginState.ERROR
      context.error = error as MicroCoreError

      // 触发错误事件
      plugin['emit']({
        type: 'error',
        plugin,
        timestamp: Date.now(),
        error: error as MicroCoreError
      })

      throw error
    }
  }

  /**
   * 卸载插件
   * @param plugin 插件实例
   */
  async uninstallPlugin(plugin: PluginBase): Promise<void> {
    const context = plugin.getContext()
    if (!context) {
      throw new Error(`插件 ${plugin.name} 未安装`)
    }

    try {
      // 如果插件处于激活状态，先停用
      if (context.state === PluginState.ACTIVE) {
        await this.deactivatePlugin(plugin)
      }

      context.state = PluginState.UNINSTALLING

      // 执行卸载前钩子
      await this.executeHook('beforeUninstall', plugin, context)

      // 执行插件卸载
      await plugin.uninstall()

      // 更新状态
      context.state = PluginState.UNINSTALLED

      // 执行卸载后钩子
      await this.executeHook('afterUninstall', plugin, context)

      // 触发卸载事件
      plugin['emit']({
        type: 'uninstall',
        plugin,
        timestamp: Date.now()
      })

    } catch (error) {
      context.state = PluginState.ERROR
      context.error = error as MicroCoreError

      // 触发错误事件
      plugin['emit']({
        type: 'error',
        plugin,
        timestamp: Date.now(),
        error: error as MicroCoreError
      })

      throw error
    }
  }

  /**
   * 激活插件
   * @param plugin 插件实例
   */
  async activatePlugin(plugin: PluginBase): Promise<void> {
    const context = plugin.getContext()
    if (!context) {
      throw new Error(`插件 ${plugin.name} 未安装`)
    }

    if (context.state !== PluginState.INSTALLED && context.state !== PluginState.INACTIVE) {
      throw new Error(`插件 ${plugin.name} 状态不正确，无法激活`)
    }

    try {
      context.state = PluginState.ACTIVATING

      // 执行激活前钩子
      await this.executeHook('beforeActivate', plugin, context)

      // 执行插件激活
      await plugin.activate()

      // 更新状态
      context.state = PluginState.ACTIVE
      context.activatedAt = Date.now()

      // 执行激活后钩子
      await this.executeHook('afterActivate', plugin, context)

      // 触发激活事件
      plugin['emit']({
        type: 'activate',
        plugin,
        timestamp: Date.now()
      })

    } catch (error) {
      context.state = PluginState.ERROR
      context.error = error as MicroCoreError

      // 触发错误事件
      plugin['emit']({
        type: 'error',
        plugin,
        timestamp: Date.now(),
        error: error as MicroCoreError
      })

      throw error
    }
  }

  /**
   * 停用插件
   * @param plugin 插件实例
   */
  async deactivatePlugin(plugin: PluginBase): Promise<void> {
    const context = plugin.getContext()
    if (!context) {
      throw new Error(`插件 ${plugin.name} 未安装`)
    }

    if (context.state !== PluginState.ACTIVE) {
      throw new Error(`插件 ${plugin.name} 未激活，无法停用`)
    }

    try {
      context.state = PluginState.DEACTIVATING

      // 执行停用前钩子
      await this.executeHook('beforeDeactivate', plugin, context)

      // 执行插件停用
      await plugin.deactivate()

      // 更新状态
      context.state = PluginState.INACTIVE

      // 执行停用后钩子
      await this.executeHook('afterDeactivate', plugin, context)

      // 触发停用事件
      plugin['emit']({
        type: 'deactivate',
        plugin,
        timestamp: Date.now()
      })

    } catch (error) {
      context.state = PluginState.ERROR
      context.error = error as MicroCoreError

      // 触发错误事件
      plugin['emit']({
        type: 'error',
        plugin,
        timestamp: Date.now(),
        error: error as MicroCoreError
      })

      throw error
    }
  }

  /**
   * 获取所有已注册的钩子
   */
  getHooks(): Map<LifecycleHook, LifecycleHookFunction[]> {
    return new Map(this.hooks)
  }

  /**
   * 清除所有钩子
   */
  clearHooks(): void {
    this.hooks.forEach(hooks => hooks.length = 0)
  }

  /**
   * 清除指定类型的钩子
   * @param hook 钩子类型
   */
  clearHook(hook: LifecycleHook): void {
    const hooks = this.hooks.get(hook)
    if (hooks) {
      hooks.length = 0
    }
  }
}