/**
 * 插件基类
 * 所有插件都必须继承此基类
 */

import type { MicroCore } from '@micro-core/core'
import type { MicroCoreError } from '@micro-core/shared'

/**
 * 插件生命周期状态
 */
export enum PluginState {
  UNINSTALLED = 'uninstalled',
  INSTALLING = 'installing',
  INSTALLED = 'installed',
  ACTIVATING = 'activating',
  ACTIVE = 'active',
  DEACTIVATING = 'deactivating',
  INACTIVE = 'inactive',
  UNINSTALLING = 'uninstalling',
  ERROR = 'error'
}

/**
 * 插件配置接口
 */
export interface PluginConfig {
  enabled?: boolean
  priority?: number
  dependencies?: string[]
  options?: Record<string, any>
}

/**
 * 插件元数据接口
 */
export interface PluginMetadata {
  name: string
  version: string
  description?: string
  author?: string
  homepage?: string
  keywords?: string[]
  dependencies?: string[]
  peerDependencies?: string[]
}

/**
 * 插件上下文接口
 */
export interface PluginContext {
  core: MicroCore
  config: PluginConfig
  state: PluginState
  metadata: PluginMetadata
  createdAt: number
  installedAt?: number
  activatedAt?: number
  error?: MicroCoreError
}

/**
 * 插件事件接口
 */
export interface PluginEvent {
  type: 'install' | 'uninstall' | 'activate' | 'deactivate' | 'error'
  plugin: PluginBase
  timestamp: number
  data?: any
  error?: MicroCoreError
}

/**
 * 插件事件监听器
 */
export type PluginEventListener = (event: PluginEvent) => void

/**
 * 插件基类抽象类
 */
export abstract class PluginBase {
  /**
   * 插件名称
   */
  abstract readonly name: string

  /**
   * 插件版本
   */
  abstract readonly version: string

  /**
   * 插件描述
   */
  abstract readonly description?: string

  /**
   * 插件作者
   */
  abstract readonly author?: string

  /**
   * 插件主页
   */
  abstract readonly homepage?: string

  /**
   * 插件关键词
   */
  abstract readonly keywords?: string[]

  /**
   * 插件依赖
   */
  abstract readonly dependencies?: string[]

  /**
   * 插件对等依赖
   */
  abstract readonly peerDependencies?: string[]

  /**
   * 插件上下文
   */
  protected context?: PluginContext

  /**
   * 事件监听器
   */
  private eventListeners: Map<string, PluginEventListener[]> = new Map()

  /**
   * 获取插件元数据
   */
  getMetadata(): PluginMetadata {
    return {
      name: this.name,
      version: this.version,
      description: this.description,
      author: this.author,
      homepage: this.homepage,
      keywords: this.keywords,
      dependencies: this.dependencies,
      peerDependencies: this.peerDependencies
    }
  }

  /**
   * 获取插件上下文
   */
  getContext(): PluginContext | undefined {
    return this.context
  }

  /**
   * 设置插件上下文
   */
  setContext(context: PluginContext): void {
    this.context = context
  }

  /**
   * 获取插件状态
   */
  getState(): PluginState {
    return this.context?.state || PluginState.UNINSTALLED
  }

  /**
   * 检查插件是否已安装
   */
  isInstalled(): boolean {
    const state = this.getState()
    return state !== PluginState.UNINSTALLED && state !== PluginState.ERROR
  }

  /**
   * 检查插件是否已激活
   */
  isActive(): boolean {
    return this.getState() === PluginState.ACTIVE
  }

  /**
   * 安装插件
   * @param core 微前端核心实例
   * @param config 插件配置
   */
  abstract install(core: MicroCore, config?: PluginConfig): Promise<void>

  /**
   * 卸载插件
   */
  abstract uninstall(): Promise<void>

  /**
   * 激活插件
   */
  abstract activate(): Promise<void>

  /**
   * 停用插件
   */
  abstract deactivate(): Promise<void>

  /**
   * 插件配置更新
   * @param config 新的配置
   */
  async updateConfig(config: PluginConfig): Promise<void> {
    if (this.context) {
      this.context.config = { ...this.context.config, ...config }
      await this.onConfigUpdate?.(config)
    }
  }

  /**
   * 配置更新钩子（可选实现）
   * @param config 新的配置
   */
  protected onConfigUpdate?(config: PluginConfig): Promise<void>

  /**
   * 插件初始化钩子（可选实现）
   */
  protected onInit?(): Promise<void>

  /**
   * 插件销毁钩子（可选实现）
   */
  protected onDestroy?(): Promise<void>

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器函数
   */
  addEventListener(event: string, listener: PluginEventListener): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(listener)
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器函数
   */
  removeEventListener(event: string, listener: PluginEventListener): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   * @param event 事件对象
   */
  protected emit(event: PluginEvent): void {
    const listeners = this.eventListeners.get(event.type)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event)
        } catch (error) {
          console.error(`Error in plugin event listener:`, error)
        }
      })
    }
  }

  /**
   * 创建插件事件
   * @param type 事件类型
   * @param data 事件数据
   * @param error 错误信息
   */
  protected createEvent(
    type: PluginEvent['type'],
    data?: any,
    error?: MicroCoreError
  ): PluginEvent {
    return {
      type,
      plugin: this,
      timestamp: Date.now(),
      data,
      error
    }
  }

  /**
   * 验证插件依赖
   * @param availablePlugins 可用插件列表
   */
  validateDependencies(availablePlugins: string[]): boolean {
    if (!this.dependencies) {
      return true
    }

    return this.dependencies.every(dep => availablePlugins.includes(dep))
  }

  /**
   * 获取插件信息字符串
   */
  toString(): string {
    return `${this.name}@${this.version}`
  }
}