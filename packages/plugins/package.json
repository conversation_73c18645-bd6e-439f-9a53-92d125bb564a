{"name": "@micro-core/plugins", "version": "0.1.0", "description": "微前端插件系统，支持按需加载和动态扩展", "type": "module", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"dev": "vite build --watch", "build": "vite build && tsc --emitDeclarationOnly", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "keywords": ["micro-frontend", "plugin", "system", "dynamic", "extensible"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/plugins"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*"}, "devDependencies": {"@types/node": "^20.10.5", "rimraf": "^5.0.5", "typescript": "^5.7.2", "vite": "^7.0.6", "vite-plugin-dts": "^4.5.4", "vitest": "^3.2.4"}, "peerDependencies": {"@micro-core/core": "0.1.0"}}