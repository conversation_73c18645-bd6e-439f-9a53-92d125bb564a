/**
 * 全局变量隔离
 * 实现全局变量的隔离，防止全局污染
 */

import { logger } from '@micro-core/shared'

/**
 * 全局隔离配置
 */
export interface GlobalIsolationConfig {
  whitelist?: string[]
  blacklist?: string[]
  strictMode?: boolean
  preserveGlobals?: boolean
  customGlobals?: Record<string, any>
}

/**
 * 全局变量快照
 */
interface GlobalSnapshot {
  added: string[]
  modified: Record<string, { original: any; current: any }>
  deleted: string[]
}

/**
 * 全局变量隔离器
 */
export class GlobalIsolation {
  private config: GlobalIsolationConfig
  private originalWindow: Record<string, any> = {}
  private proxyWindow: WindowProxy | null = null
  private globalSnapshot: GlobalSnapshot | null = null
  private isActive = false

  // 默认白名单 - 不应该被隔离的全局变量
  private readonly defaultWhitelist = [
    'console',
    'setTimeout',
    'setInterval',
    'clearTimeout',
    'clearInterval',
    'requestAnimationFrame',
    'cancelAnimationFrame',
    'fetch',
    'XMLHttpRequest',
    'Promise',
    'Array',
    'Object',
    'String',
    'Number',
    'Boolean',
    'Date',
    'RegExp',
    'Error',
    'JSON',
    'Math',
    'parseInt',
    'parseFloat',
    'isNaN',
    'isFinite',
    'encodeURIComponent',
    'decodeURIComponent',
    'encodeURI',
    'decodeURI',
    'btoa',
    'atob'
  ]

  // 默认黑名单 - 应该被隔离的全局变量
  private readonly defaultBlacklist = [
    'eval',
    'Function'
  ]

  constructor(config: GlobalIsolationConfig = {}) {
    this.config = {
      whitelist: [...this.defaultWhitelist, ...(config.whitelist || [])],
      blacklist: [...this.defaultBlacklist, ...(config.blacklist || [])],
      strictMode: true,
      preserveGlobals: false,
      customGlobals: {},
      ...config
    }
  }

  /**
   * 创建全局隔离环境
   */
  createIsolation(): WindowProxy {
    try {
      if (this.isActive) {
        logger.warn('全局隔离环境已经激活')
        return this.proxyWindow!
      }

      // 创建全局快照
      this.createGlobalSnapshot()

      // 创建代理窗口
      this.proxyWindow = this.createProxyWindow()

      this.isActive = true
      logger.debug('全局隔离环境创建成功')

      return this.proxyWindow
    } catch (error) {
      logger.error('创建全局隔离环境失败:', error)
      throw error
    }
  }

  /**
   * 销毁全局隔离环境
   */
  destroyIsolation(): void {
    try {
      if (!this.isActive) {
        logger.warn('全局隔离环境未激活')
        return
      }

      // 恢复全局状态
      if (this.config.preserveGlobals && this.globalSnapshot) {
        this.restoreGlobalState()
      }

      // 清理状态
      this.proxyWindow = null
      this.globalSnapshot = null
      this.originalWindow = {}
      this.isActive = false

      logger.debug('全局隔离环境销毁成功')
    } catch (error) {
      logger.error('销毁全局隔离环境失败:', error)
    }
  }

  /**
   * 创建全局快照
   */
  private createGlobalSnapshot(): void {
    this.globalSnapshot = {
      added: [],
      modified: {},
      deleted: []
    }

    // 保存当前全局变量状态
    for (const key in window) {
      if (this.shouldCaptureProperty(key)) {
        try {
          this.originalWindow[key] = (window as any)[key]
        } catch (error) {
          // 某些属性可能无法访问
          logger.debug(`无法访问全局属性: ${key}`, error)
        }
      }
    }
  }

  /**
   * 创建代理窗口
   */
  private createProxyWindow(): WindowProxy {
    const fakeWindow = Object.create(null)
    
    // 添加自定义全局变量
    Object.assign(fakeWindow, this.config.customGlobals)

    return new Proxy(fakeWindow, {
      get: (target, property, receiver) => {
        const key = String(property)

        // 检查黑名单
        if (this.config.blacklist?.includes(key)) {
          logger.warn(`访问被禁止的全局变量: ${key}`)
          return undefined
        }

        // 优先从代理对象获取
        if (Reflect.has(target, property)) {
          return Reflect.get(target, property, receiver)
        }

        // 从原始window获取白名单属性
        if (this.config.whitelist?.includes(key)) {
          return (window as any)[key]
        }

        // 严格模式下，未知属性返回undefined
        if (this.config.strictMode) {
          return undefined
        }

        // 非严格模式下，从原始window获取
        return (window as any)[key]
      },

      set: (target, property, value, receiver) => {
        const key = String(property)

        // 检查黑名单
        if (this.config.blacklist?.includes(key)) {
          logger.warn(`设置被禁止的全局变量: ${key}`)
          return false
        }

        // 记录全局变量变化
        this.recordGlobalChange(key, value)

        // 白名单属性设置到原始window
        if (this.config.whitelist?.includes(key)) {
          try {
            (window as any)[key] = value
            return true
          } catch (error) {
            logger.warn(`设置全局变量失败: ${key}`, error)
            return false
          }
        }

        // 其他属性设置到代理对象
        return Reflect.set(target, property, value, receiver)
      },

      has: (target, property) => {
        const key = String(property)

        // 检查黑名单
        if (this.config.blacklist?.includes(key)) {
          return false
        }

        // 检查代理对象
        if (Reflect.has(target, property)) {
          return true
        }

        // 检查白名单
        if (this.config.whitelist?.includes(key)) {
          return key in window
        }

        // 严格模式下，只检查代理对象
        if (this.config.strictMode) {
          return false
        }

        // 非严格模式下，检查原始window
        return key in window
      },

      ownKeys: (target) => {
        const keys = Reflect.ownKeys(target)
        
        // 添加白名单属性
        if (this.config.whitelist) {
          for (const key of this.config.whitelist) {
            if (key in window && !keys.includes(key)) {
              keys.push(key)
            }
          }
        }

        return keys
      },

      getOwnPropertyDescriptor: (target, property) => {
        const key = String(property)

        // 检查代理对象
        const desc = Reflect.getOwnPropertyDescriptor(target, property)
        if (desc) {
          return desc
        }

        // 检查白名单
        if (this.config.whitelist?.includes(key)) {
          return Reflect.getOwnPropertyDescriptor(window, property)
        }

        return undefined
      },

      defineProperty: (target, property, descriptor) => {
        const key = String(property)

        // 检查黑名单
        if (this.config.blacklist?.includes(key)) {
          logger.warn(`定义被禁止的全局变量: ${key}`)
          return false
        }

        // 记录全局变量变化
        this.recordGlobalChange(key, descriptor.value)

        return Reflect.defineProperty(target, property, descriptor)
      },

      deleteProperty: (target, property) => {
        const key = String(property)

        // 检查黑名单
        if (this.config.blacklist?.includes(key)) {
          logger.warn(`删除被禁止的全局变量: ${key}`)
          return false
        }

        // 记录删除操作
        this.recordGlobalDeletion(key)

        return Reflect.deleteProperty(target, property)
      }
    })
  }

  /**
   * 记录全局变量变化
   */
  private recordGlobalChange(key: string, value: any): void {
    if (!this.globalSnapshot) return

    const originalValue = this.originalWindow[key]

    if (originalValue === undefined) {
      // 新增的全局变量
      if (!this.globalSnapshot.added.includes(key)) {
        this.globalSnapshot.added.push(key)
      }
    } else if (originalValue !== value) {
      // 修改的全局变量
      this.globalSnapshot.modified[key] = {
        original: originalValue,
        current: value
      }
    }
  }

  /**
   * 记录全局变量删除
   */
  private recordGlobalDeletion(key: string): void {
    if (!this.globalSnapshot) return

    if (this.originalWindow[key] !== undefined) {
      if (!this.globalSnapshot.deleted.includes(key)) {
        this.globalSnapshot.deleted.push(key)
      }
    }
  }

  /**
   * 恢复全局状态
   */
  private restoreGlobalState(): void {
    if (!this.globalSnapshot) return

    try {
      // 删除新增的全局变量
      for (const key of this.globalSnapshot.added) {
        try {
          delete (window as any)[key]
        } catch (error) {
          logger.warn(`删除全局变量失败: ${key}`, error)
        }
      }

      // 恢复修改的全局变量
      for (const [key, { original }] of Object.entries(this.globalSnapshot.modified)) {
        try {
          (window as any)[key] = original
        } catch (error) {
          logger.warn(`恢复全局变量失败: ${key}`, error)
        }
      }

      // 恢复删除的全局变量
      for (const key of this.globalSnapshot.deleted) {
        try {
          if (this.originalWindow[key] !== undefined) {
            (window as any)[key] = this.originalWindow[key]
          }
        } catch (error) {
          logger.warn(`恢复删除的全局变量失败: ${key}`, error)
        }
      }

      logger.debug('全局状态恢复成功')
    } catch (error) {
      logger.error('恢复全局状态失败:', error)
    }
  }

  /**
   * 判断是否应该捕获属性
   */
  private shouldCaptureProperty(key: string): boolean {
    // 跳过原型链属性
    if (!window.hasOwnProperty(key)) {
      return false
    }

    // 跳过函数属性（除了白名单）
    try {
      const value = (window as any)[key]
      if (typeof value === 'function' && !this.config.whitelist?.includes(key)) {
        return false
      }
    } catch (error) {
      return false
    }

    return true
  }

  /**
   * 获取隔离状态
   */
  getIsolationState() {
    return {
      isActive: this.isActive,
      config: this.config,
      snapshot: this.globalSnapshot,
      originalKeys: Object.keys(this.originalWindow).length,
      hasProxy: !!this.proxyWindow
    }
  }

  /**
   * 获取全局变化差异
   */
  getGlobalDiff(): GlobalSnapshot | null {
    return this.globalSnapshot
  }
}

/**
 * 创建全局隔离实例
 */
export function createGlobalIsolation(config?: GlobalIsolationConfig): GlobalIsolation {
  return new GlobalIsolation(config)
}