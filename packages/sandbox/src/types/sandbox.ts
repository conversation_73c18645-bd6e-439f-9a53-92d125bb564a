/**
 * 沙箱类型定义
 */

/**
 * 沙箱类型枚举
 */
export enum SandboxType {
  PROXY = 'proxy',
  DEFINE_PROPERTY = 'defineProperty',
  IFRAME = 'iframe',
  WEB_COMPONENT = 'webComponent',
  NAMESPACE = 'namespace',
  FEDERATION = 'federation'
}

/**
 * 沙箱状态枚举
 */
export enum SandboxStatus {
  IDLE = 'idle',
  CREATING = 'creating',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DESTROYING = 'destroying',
  DESTROYED = 'destroyed',
  ERROR = 'error'
}

/**
 * 沙箱配置
 */
export interface SandboxConfig {
  type: SandboxType
  name: string
  strictMode?: boolean
  enableCSS?: boolean
  enableJS?: boolean
  enableGlobal?: boolean
  customProps?: Record<string, any>
  performance?: {
    enableMonitoring?: boolean
    maxMemoryUsage?: number
    maxExecutionTime?: number
  }
}

/**
 * 沙箱上下文
 */
export interface SandboxContext {
  id: string
  name: string
  type: SandboxType
  status: SandboxStatus
  window: WindowProxy
  document: Document
  container: HTMLElement
  createdAt: number
  lastActiveAt: number
  config: SandboxConfig
}

/**
 * 沙箱性能指标
 */
export interface SandboxPerformance {
  memoryUsage: number
  executionTime: number
  domNodes: number
  eventListeners: number
  timers: number
  creationTime: number
  activationTime: number
}

/**
 * 沙箱事件类型
 */
export enum SandboxEventType {
  CREATED = 'created',
  ACTIVATED = 'activated',
  DEACTIVATED = 'deactivated',
  DESTROYED = 'destroyed',
  ERROR = 'error',
  PERFORMANCE_WARNING = 'performanceWarning'
}

/**
 * 沙箱事件
 */
export interface SandboxEvent {
  type: SandboxEventType
  sandboxId: string
  timestamp: number
  data?: any
  error?: Error
}

/**
 * 沙箱监听器
 */
export type SandboxEventListener = (event: SandboxEvent) => void

/**
 * 沙箱管理器接口
 */
export interface ISandboxManager {
  createSandbox(config: SandboxConfig): Promise<SandboxContext>
  activateSandbox(sandboxId: string): Promise<void>
  deactivateSandbox(sandboxId: string): Promise<void>
  destroySandbox(sandboxId: string): Promise<void>
  getSandbox(sandboxId: string): SandboxContext | undefined
  getAllSandboxes(): SandboxContext[]
  getActiveSandboxes(): SandboxContext[]
  addEventListener(type: SandboxEventType, listener: SandboxEventListener): void
  removeEventListener(type: SandboxEventType, listener: SandboxEventListener): void
  getPerformance(sandboxId: string): SandboxPerformance | undefined
}

/**
 * 沙箱工厂接口
 */
export interface ISandboxFactory {
  createSandbox(type: SandboxType): ISandboxStrategy
  selectOptimalStrategy(env: Environment): SandboxType
  getSupportedTypes(): SandboxType[]
  isTypeSupported(type: SandboxType): boolean
}

/**
 * 环境信息
 */
export interface Environment {
  userAgent: string
  browser: {
    name: string
    version: string
  }
  features: {
    proxy: boolean
    shadowDOM: boolean
    webComponents: boolean
    iframe: boolean
    defineProperty: boolean
  }
  performance: {
    memory: number
    cores: number
  }
}