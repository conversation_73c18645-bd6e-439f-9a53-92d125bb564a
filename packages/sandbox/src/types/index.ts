/**
 * 沙箱类型定义主入口
 */

// 导出沙箱相关类型
export * from './sandbox'
export * from './strategy'
export * from './isolation'

// 重新导出核心类型
export type {
  SandboxType,
  SandboxState,
  SandboxOptions,
  SandboxContext,
  SandboxPerformance,
  SandboxEvent,
  SandboxEventListener,
  ISandbox,
  ISandboxManager
} from './sandbox'

export type {
  SandboxStrategyType,
  SandboxStrategyOptions,
  SandboxStrategyContext,
  SandboxStrategyPerformance,
  ISandboxStrategy,
  ISandboxFactory,
  IStrategySelector
} from './strategy'

export type {
  JavaScriptIsolationConfig,
  CSSIsolationConfig,
  GlobalIsolationConfig,
  IsolationContext,
  IIsolationManager,
  IsolationPerformance,
  IsolationEvent,
  IsolationEventListener,
  IsolationStrategy
} from './isolation'