/**
 * 沙箱策略类型定义
 */

import type { SandboxContext, SandboxConfig, SandboxPerformance } from './sandbox'

/**
 * 沙箱策略接口
 */
export interface ISandboxStrategy {
  readonly type: string
  readonly name: string
  readonly version: string
  
  create(config: SandboxConfig): Promise<SandboxContext>
  activate(context: SandboxContext): Promise<void>
  deactivate(context: SandboxContext): Promise<void>
  destroy(context: SandboxContext): Promise<void>
  isolate(context: SandboxContext, code: string): Promise<any>
  getPerformance(context: SandboxContext): SandboxPerformance
  isSupported(): boolean
}

/**
 * Proxy沙箱配置
 */
export interface ProxySandboxConfig extends SandboxConfig {
  proxyOptions?: {
    interceptGlobals?: boolean
    whitelistGlobals?: string[]
    blacklistGlobals?: string[]
  }
}

/**
 * DefineProperty沙箱配置
 */
export interface DefinePropertySandboxConfig extends SandboxConfig {
  definePropertyOptions?: {
    preserveDescriptors?: boolean
    interceptSetters?: boolean
    interceptGetters?: boolean
  }
}

/**
 * iframe沙箱配置
 */
export interface IframeSandboxConfig extends SandboxConfig {
  iframeOptions?: {
    src?: string
    sandbox?: string
    allowScripts?: boolean
    allowSameOrigin?: boolean
    allowForms?: boolean
    allowPopups?: boolean
    allowModals?: boolean
  }
}

/**
 * WebComponent沙箱配置
 */
export interface WebComponentSandboxConfig extends SandboxConfig {
  webComponentOptions?: {
    shadowMode?: 'open' | 'closed'
    delegatesFocus?: boolean
    slotAssignment?: 'named' | 'manual'
  }
}

/**
 * Namespace沙箱配置
 */
export interface NamespaceSandboxConfig extends SandboxConfig {
  namespaceOptions?: {
    prefix?: string
    separator?: string
    preserveGlobals?: boolean
  }
}

/**
 * Federation沙箱配置
 */
export interface FederationSandboxConfig extends SandboxConfig {
  federationOptions?: {
    moduleName?: string
    exposes?: Record<string, string>
    remotes?: Record<string, string>
    shared?: Record<string, any>
  }
}

/**
 * 策略工厂配置
 */
export interface StrategyFactoryConfig {
  defaultStrategy?: string
  fallbackStrategy?: string
  enableAutoSelection?: boolean
  performanceThreshold?: {
    memory?: number
    cpu?: number
    time?: number
  }
}

/**
 * 策略选择器接口
 */
export interface IStrategySelector {
  selectStrategy(config: SandboxConfig, env?: any): string
  getRecommendedStrategy(requirements: StrategyRequirements): string
  isStrategySupported(strategy: string): boolean
  getStrategyScore(strategy: string, requirements: StrategyRequirements): number
}

/**
 * 策略需求
 */
export interface StrategyRequirements {
  isolation?: {
    js?: boolean
    css?: boolean
    dom?: boolean
  }
  performance?: {
    priority?: 'high' | 'medium' | 'low'
    memoryLimit?: number
    cpuLimit?: number
  }
  compatibility?: {
    browsers?: string[]
    features?: string[]
  }
  security?: {
    level?: 'strict' | 'moderate' | 'loose'
    allowEval?: boolean
    allowDynamicImport?: boolean
  }
}

/**
 * 策略评分权重
 */
export interface StrategyScoreWeights {
  performance: number
  isolation: number
  compatibility: number
  security: number
  stability: number
}